{"name": "monitor", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@ant-design/icons": "^5.6.1", "@hookform/resolvers": "^5.0.1", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^13.5.0", "@types/file-saver": "^2.0.7", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.17", "@types/node": "^16.18.126", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "antd": "^5.25.1", "axios": "^1.9.0", "babel-plugin-module-resolver": "^5.0.2", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "history": "^5.3.0", "immer": "^10.1.1", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-router-dom": "^6.22.0", "react-scripts": "5.0.1", "react-toastify": "^10.0.4", "recharts": "^2.12.7", "typescript": "^4.9.5", "uuid": "^11.1.0", "web-vitals": "^2.1.4", "yup": "^1.6.1", "zod": "^3.24.4", "zustand": "^5.0.5"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "start": "webpack serve --config webpack.config.ts --env environment=development", "start:daotao": "webpack serve --config webpack.config.ts --env environment=staging", "build": "npm run clean && webpack --config webpack.config.ts --env environment=production --env treeshake=yes", "build:analyze": "npm run clean && webpack --config webpack.config.ts --env environment=production --env treeshake=yes --env analyze=yes", "format": "npx prettier --write .", "lint": "eslint --ext .js,.jsx,.ts,.tsx src/", "lint:fix": "eslint --ext .js,.jsx,.ts,.tsx src/ --fix", "test": "react-scripts test", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "clean-webpack-plugin": "^4.0.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "eslint": "^9.27.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.1.0", "html-webpack-plugin": "^5.6.3", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "prettier": "^3.5.3", "react-devtools": "^6.1.2", "sass": "^1.89.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "tailwindcss": "^3.4.17", "ts-loader": "^9.5.2", "typescript-eslint": "^8.32.1", "webpack": "^5.99.8", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}